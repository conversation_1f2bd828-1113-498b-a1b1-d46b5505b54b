const ActiveDirectory = require('activedirectory2');
const ad = new ActiveDirectory({
    url: 'ldap://lonzagroup.net',
    baseDN: 'ou=NanSha,dc=lonzagroup,dc=net',
    username: '<EMAIL>',
    password: 'Fallingstar@1984-'
});

ad.authenticate('<EMAIL>', 'Fallingstar@1984-', function (err, auth) {
    if (err) {
        console.error('绑定账户登录失败:', err);
        return;
    }
    if (!auth) {
        console.error('绑定账户密码错误');
        return;
    }

    console.log('绑定账户登录成功，开始查询用户信息...\n');


    ad.findUser('hhon', (err, user) => {
        if (err) {
            console.error('查询失败:', err);
            return;
        }

        if (!user) {
            console.log('未找到用户 xxi');
            return;
        }

        // 3. 打印完整对象
        console.log('用户完整信息:');
        console.log(JSON.stringify(user, null, 2));

        // 4. 打印常用字段示例
        console.log('\n常用字段:');
        console.log('sAMAccountName :', user.sAMAccountName);
        console.log('displayName    :', user.displayName);
        console.log('mail           :', user.mail);

    });
});

