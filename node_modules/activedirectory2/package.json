{"author": "<PERSON> <<EMAIL>>", "name": "activedirectory2", "version": "2.2.0", "description": "ActiveDirectory is an ldapjs client for authN (authentication) and authZ (authorization) for Microsoft Active Directory with range retrieval support for large Active Directory installations.", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jrfom.com"}], "main": "index.js", "directories": {"lib": "./lib"}, "engines": {"node": ">=4.0"}, "keywords": ["ldap", "active directory"], "bugs": {"url": "https://github.com/jsumners/node-activedirectory/issues"}, "dependencies": {"abstract-logging": "^2.0.0", "async": "^3.1.0", "ldapjs": "^2.3.3", "merge-options": "^2.0.0"}, "scripts": {"lint": "standard | snazzy", "lint:ci": "standard", "test": "tap --no-coverage-report", "test:ci": "tap", "doc": "jsdoc -c jsdoc.json"}, "precommit": ["lint", "test"], "repository": {"type": "git", "url": "https://github.com/jsumners/node-activedirectory.git"}, "license": "MIT", "devDependencies": {"jsdoc": "^3.6.5", "pino": "^6.4.1", "pre-commit": "^1.2.2", "snazzy": "^8.0.0", "standard": "^14.3.4", "tap": "^14.10.8"}}