name: Continuous Integration
on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node:
          - '10'
          - '12'
          # - '13'
    name: Node ${{ matrix.node }}
    steps:
      - uses: actions/checkout@v1
      - name: Setup node
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node }}
      - run: npm install
      - run: npm run lint:ci
      - run: npm run test:ci
