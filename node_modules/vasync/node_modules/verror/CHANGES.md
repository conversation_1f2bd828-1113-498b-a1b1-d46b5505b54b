# Changelog

## Not yet released

None yet.

## v1.10.0

* #49 want convenience functions for MultiErrors

## v1.9.0

* #47 could use VError.hasCauseWithName()

## v1.8.1

* #39 captureStackTrace lost when inheriting from WError

## v1.8.0

* #23 Preserve original stack trace(s)

## v1.7.0

* #10 better support for extra properties on Errors
* #11 make it easy to find causes of a particular kind
* #29 No documentation on how to Install this package
* #36 elide development-only files from npm package
