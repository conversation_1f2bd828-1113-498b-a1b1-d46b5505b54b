{"author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "name": "ldap-filter", "homepage": "http://ldapjs.org", "description": "API for handling LDAP-style filters", "version": "0.3.3", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/pfmooney/node-ldap-filter.git"}, "main": "lib/index.js", "directories": {"lib": "./lib"}, "engines": {"node": ">=0.8"}, "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"faucet": "0.0.1", "istanbul": "^0.4.0", "tape": "^4.2.2"}, "scripts": {"test": "./node_modules/.bin/istanbul cover --print none test/run.js | ./node_modules/.bin/faucet"}}