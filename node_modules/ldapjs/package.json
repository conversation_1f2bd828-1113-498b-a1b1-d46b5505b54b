{"originalAuthor": "<PERSON> <<EMAIL>>", "name": "ldapjs", "homepage": "http://ldapjs.org", "description": "LDAP client and server APIs", "version": "2.3.3", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/ldapjs/node-ldapjs.git"}, "main": "lib/index.js", "directories": {"lib": "./lib"}, "engines": {"node": ">=10.13.0"}, "dependencies": {"abstract-logging": "^2.0.0", "asn1": "^0.2.4", "assert-plus": "^1.0.0", "backoff": "^2.5.0", "ldap-filter": "^0.3.3", "once": "^1.4.0", "vasync": "^2.2.0", "verror": "^1.8.1"}, "devDependencies": {"eslint": "^7.20.0", "eslint-config-standard": "^16.0.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "front-matter": "^4.0.2", "get-port": "^5.1.1", "highlight.js": "^11.0.1", "husky": "^4.2.5", "marked": "^4.0.0", "tap": "15.2.3"}, "scripts": {"test": "tap --no-cov -R terse", "test:ci": "tap --coverage-report=lcovonly -R terse", "test:cov": "tap -R terse", "test:cov:html": "tap --coverage-report=html -R terse", "test:watch": "tap -n -w --no-coverage-report -R terse", "test:integration": "tap --no-cov -R terse 'test-integration/**/*.test.js'", "test:integration:local": "docker-compose up -d && npm run test:integration && docker-compose down", "lint": "eslint . --fix", "lint:ci": "eslint .", "docs": "node scripts/build-docs.js"}, "husky": {"hooks": {"pre-commit": "npm run lint:ci && npm run test"}}}