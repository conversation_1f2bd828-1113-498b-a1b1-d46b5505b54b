name: 'Update Docs'

on:
  push:
    branches:
      - master

jobs:
  docs:
    name: Update Docs
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '14'
      - name: Install Packages
        run: npm install
      - name: Build Docs
        run: npm run docs
      - name: Deploy 🚢
        uses: cpina/github-action-push-to-another-repository@master
        env:
          API_TOKEN_GITHUB: ${{ secrets.API_TOKEN_GITHUB }}
        with:
          source-directory: 'public'
          destination-github-username: 'ldapjs'
          destination-repository-name: 'ldapjs.github.io'
          user-email: '<EMAIL>'
          target-branch: 'gh-pages'
