
/* ---- general styles */

body {
  font: 13px "Lucida Grande", "Lucida Sans Unicode", arial, sans-serif;
  line-height: 1.53846; /* 20px */
  color: #4a3f2d;
}

:focus:not(:focus-visible) {
  outline: 0;
}

h1,h2,h3 {
  font-weight:normal;
}

h3{
  margin-bottom:0;
}

ul, li {
  margin:0px;
  padding:0px;
}

ul {
  margin-left:40px;
}

ul > li {
  list-style:disc;
  list-style-position:inside;
  margin:10px 0px;
}

hr {
  border:none;
  width:98%;
  margin-left:-10px;
  border-top:1px solid #CCCCCC;
  border-bottom:1px solid #FFFFFF;
}

code,
pre {
  border:1px solid #CCCCCC;
  background:#F2F0EE;
  -webkit-border-radius:2px;
  -moz-border-radius:2px;
  border-radius:2px;
  white-space:pre-wrap;
}
code {
  padding: 0 0.2em;
}
pre {
  margin: 1em 0;
  padding: .75em;
  overflow: auto;
  padding:10px 1.2em;
  margin-top:0;
  margin-bottom:20px;
}
pre code  {
  border: medium none;
  padding: 0;
}
a code {
  text-decoration: underline;
}

a {
  color:#FD6512;
  text-decoration:none;
}

h4 {
  font-size: 85%;
  margin: 0;
  padding: 0;
  line-height: 1em;
  display: inline;
}

/* ---- header and sidebar */

#header {
  background:#C3BDB3;
  background:#1C313C;
  height:66px;
  left:0px;
  position:absolute;
  top:0px;
  width:100%;
  z-index:1;
  font-size:0.7em;
}

#header h1 {
  width: 424px;
  height: 35px;
  display:block;
  background: url(../img/logo.svg) no-repeat;
  line-height:2.1em;
  padding:0;
  padding-left:140px;
  margin-top:18px;
  margin-left:20px;
  color:white;
  text-transform: uppercase;
}

#sidebar {
  background-color:#EDEBEA;
  bottom:0px;
  left:0px;
  overflow:auto;
  padding:20px 0px 0px 15px;
  position:absolute;
  top:66px;
  width:265px;
  z-index:1;
}

#content {
  top:64px;
  bottom:0px;
  right:0px;
  left:290px;
  padding:20px 30px 400px;
  position:absolute;
  overflow:auto;
  z-index:0;
}

#sidebar h1 {
  font-size:1.2em;
  padding:0px;
  margin-top:15px;
  margin-bottom:3px;
}

#sidebar ul {
  margin:3px 0 10px 0;
}

#sidebar ul ul {
  margin:3px 0 5px 10px;
}

#sidebar li {
  margin:0;
  padding:0;
  font-size:0.9em;
}

#sidebar li,
#sidebar li a {
  color:#5C5954;
  list-style:none;
  padding:1px 0px 1px 2px;
}


/* ---- intro */

.intro {
  color:#29231A;
  padding: 22px 25px;
  background: #EDEBEA;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
  margin-bottom:40px;
}
.intro h1 {
  color: #1C313C;
}
.intro h3 {
  margin: 5px 0px 3px;
  font-size: 100%;
  font-weight: bold;
}
.intro ul {
  list-style-type:disc;
  padding-left:20px;
  margin-left:0;
}
.intro ul li{
  margin:0;
}
.intro p {
  padding-left:20px;
  margin: 5px 0px 3px;
}



h2 {
  overflow: auto;
  margin-top: 60px;
  border-top: 2px solid #979592;
  z-index: 3;
}
h1 + h2 {
  margin-top: 0px;
}

h2 span {
  background: #979592;
  float:right;
  color:#fff;
  margin:0;
  margin-left:3px;
  padding:0.3em 0.7em;
  font-size: 0.55em;
  word-spacing: 0.8em;  /* separate verb from path */
  color:#fff;
}




/*---- print media */

@media print {
  body { background:white; color:black; margin:0; }
  #sidebar {
    display: none;
  }
  #content {
    position: relative;
    padding: 5px;
    left: 0px;
    top: 0px;
  }
  h1, h2, h4 {
    page-break-after: avoid;
  }
  pre {
    page-break-inside: avoid;
  }
}

/* tables still need cellspacing="0" in the markup */
table {
  border-collapse:collapse; border-spacing:0;
  margin: 20px 0;
}
th,
td {
  border: solid #aaa;
  border-width: 1px 0;
  line-height: 23px;
  padding: 0 12px;
  text-align: left;
  vertical-align: text-bottom;
}
th {
  border-collapse: separate;
}
tbody tr:nth-child(odd) {
  background-color: #f2f0ee;
}
