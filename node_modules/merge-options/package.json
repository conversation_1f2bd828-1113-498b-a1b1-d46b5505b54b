{"name": "merge-options", "version": "2.0.0", "description": "Merge Option Objects", "license": "MIT", "repository": "schnittstabil/merge-options", "author": {"name": "<PERSON>", "email": "micha<PERSON>@schnittstabil.de"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava", "clean": "rimraf .nyc_output/ coverage/", "coverage-html": "nyc ava && nyc report --reporter=html"}, "files": ["index.js"], "keywords": ["merge", "options", "deep", "plain", "object", "extend", "clone"], "devDependencies": {"ava": "^2.4.0", "coveralls": "^3.0.3", "nyc": "^14.1.1", "rimraf": "^3.0.0", "xo": "^0.25.3"}, "dependencies": {"is-plain-obj": "^2.0.0"}}