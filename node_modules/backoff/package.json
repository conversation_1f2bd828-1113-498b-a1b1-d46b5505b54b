{"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "2.5.0", "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "keywords": ["backoff", "retry", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "dependencies": {"precond": "0.2"}, "devDependencies": {"sinon": "1.10", "nodeunit": "0.9"}, "scripts": {"docco": "docco lib/*.js lib/strategy/* index.js", "pretest": "jshint lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "files": ["index.js", "lib", "tests"]}