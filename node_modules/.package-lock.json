{"name": "Study", "lockfileVersion": 3, "requires": true, "packages": {"node_modules/abstract-logging": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/abstract-logging/-/abstract-logging-2.0.1.tgz", "integrity": "sha512-2BjRTZxTPvheOvGbBslFSYOUkr+SjPtOnrLP33f+VIWLzezQpZcqVg7ja3L4dBXmzzgwT+a029jRx5PCi3JuiA==", "license": "MIT"}, "node_modules/activedirectory2": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/activedirectory2/-/activedirectory2-2.2.0.tgz", "integrity": "sha512-uGbw74xttFG6hgocU8T1a0oDofLsyTp44BPTn42JN5C2QlyO5kRl2E7ZoUdfpFzV+yxhaQTKI+8QqRB5HONYvA==", "license": "MIT", "dependencies": {"abstract-logging": "^2.0.0", "async": "^3.1.0", "ldapjs": "^2.3.3", "merge-options": "^2.0.0"}, "engines": {"node": ">=4.0"}}, "node_modules/asn1": {"version": "0.2.6", "resolved": "https://registry.npmmirror.com/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/async": {"version": "3.2.6", "resolved": "https://registry.npmmirror.com/async/-/async-3.2.6.tgz", "integrity": "sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==", "license": "MIT"}, "node_modules/backoff": {"version": "2.5.0", "resolved": "https://registry.npmmirror.com/backoff/-/backoff-2.5.0.tgz", "integrity": "sha512-wC5ihrnUXmR2douXmXLCe5O3zg3GKIyvRi/hi58a/XyRxVI+3/yM0PYueQOZXPXQ9pxBislYkw+sF9b7C/RuMA==", "license": "MIT", "dependencies": {"precond": "0.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==", "license": "MIT"}, "node_modules/extsprintf": {"version": "1.4.1", "resolved": "https://registry.npmmirror.com/extsprintf/-/extsprintf-1.4.1.tgz", "integrity": "sha512-Wrk35e8ydCKDj/ArClo1VrPVmN8zph5V4AtHwIuHhvMXsKf73UT3BOD+azBIW+3wOJ4FhEH7zyaJCFvChjYvMA==", "engines": ["node >=0.6.0"], "license": "MIT"}, "node_modules/is-plain-obj": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-2.1.0.tgz", "integrity": "sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ldap-filter": {"version": "0.3.3", "resolved": "https://registry.npmmirror.com/ldap-filter/-/ldap-filter-0.3.3.tgz", "integrity": "sha512-/tFkx5WIn4HuO+6w9lsfxq4FN3O+fDZeO9Mek8dCD8rTUpqzRa766BOBO7BcGkn3X86m5+cBm1/2S/Shzz7gMg==", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.8"}}, "node_modules/ldapjs": {"version": "2.3.3", "resolved": "https://registry.npmmirror.com/ldapjs/-/ldapjs-2.3.3.tgz", "integrity": "sha512-75QiiLJV/PQqtpH+HGls44dXweviFwQ6SiIK27EqzKQ5jU/7UFrl2E5nLdQ3IYRBzJ/AVFJI66u0MZ0uofKYwg==", "deprecated": "This package has been decomissioned. See https://github.com/ldapjs/node-ldapjs/blob/8ffd0bc9c149088a10ec4c1ec6a18450f76ad05d/README.md", "license": "MIT", "dependencies": {"abstract-logging": "^2.0.0", "asn1": "^0.2.4", "assert-plus": "^1.0.0", "backoff": "^2.5.0", "ldap-filter": "^0.3.3", "once": "^1.4.0", "vasync": "^2.2.0", "verror": "^1.8.1"}, "engines": {"node": ">=10.13.0"}}, "node_modules/merge-options": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/merge-options/-/merge-options-2.0.0.tgz", "integrity": "sha512-S7xYIeWHl2ZUKF7SDeBhGg6rfv5bKxVBdk95s/I7wVF8d+hjLSztJ/B271cnUiF6CAFduEQ5Zn3HYwAjT16DlQ==", "license": "MIT", "dependencies": {"is-plain-obj": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/precond": {"version": "0.2.3", "resolved": "https://registry.npmmirror.com/precond/-/precond-0.2.3.tgz", "integrity": "sha512-QCYG84SgGyGzqJ/vlMsxeXd/pgL/I94ixdNFyh1PusWmTCyVfPJjZ1K1jvHtsbfnXQs2TSkEP2fR7QiMZAnKFQ==", "engines": {"node": ">= 0.6"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "license": "MIT"}, "node_modules/vasync": {"version": "2.2.1", "resolved": "https://registry.npmmirror.com/vasync/-/vasync-2.2.1.tgz", "integrity": "sha512-Hq72JaTpcTFdWiNA4Y22Amej2GH3BFmBaKPPlDZ4/oC8HNn2ISHLkFrJU4Ds8R3jcUi7oo5Y9jcMHKjES+N9wQ==", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"verror": "1.10.0"}}, "node_modules/vasync/node_modules/verror": {"version": "1.10.0", "resolved": "https://registry.npmmirror.com/verror/-/verror-1.10.0.tgz", "integrity": "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/verror": {"version": "1.10.1", "resolved": "https://registry.npmmirror.com/verror/-/verror-1.10.1.tgz", "integrity": "sha512-veufcmxri4e3XSrT0xwfUR7kguIkaxBeosDg00yDWhk49wdwkSUrvvsm7nc75e1PUyvIeZj6nS8VQRYz2/S4Xg==", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "license": "ISC"}}}